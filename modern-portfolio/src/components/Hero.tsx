'use client';

import { motion } from 'framer-motion';
import { Send, Palette } from 'lucide-react';
import Image from 'next/image';

const Hero = () => {
  // Tech icons for floating animation
  const techIcons = [
    { src: '/images/1.png', name: 'HTML', position: { top: '20%', right: '15%' } },
    { src: '/images/2.png', name: 'CSS', position: { top: '35%', right: '8%' } },
    { src: '/images/3.webp', name: 'J<PERSON>', position: { top: '50%', right: '12%' } },
    { src: '/images/4.webp', name: 'React', position: { top: '65%', right: '18%' } },
    { src: '/images/5.png', name: 'Node', position: { top: '25%', right: '25%' } },
    { src: '/images/6.png', name: 'MongoDB', position: { top: '45%', right: '28%' } },
    { src: '/images/7.png', name: 'Git', position: { top: '60%', right: '22%' } },
    { src: '/images/8.png', name: '<PERSON>gma', position: { top: '30%', right: '35%' } },
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Video */}
      <div className="absolute inset-0 z-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover opacity-40"
        >
          <source src="/videos/galaxy.mp4" type="video/mp4" />
        </video>
        <div className="absolute inset-0 bg-gray-900/60"></div>
      </div>

      {/* Central Blackhole Video */}
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 md:w-96 md:h-96 z-10"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 2, delay: 0.5 }}
      >
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover"
        >
          <source src="/videos/blackhole.mp4" type="video/mp4" />
        </video>
      </motion.div>

      {/* Floating Tech Icons */}
      {techIcons.map((icon, index) => (
        <motion.div
          key={icon.name}
          className="absolute w-12 h-12 md:w-16 md:h-16 z-20"
          style={icon.position}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: 1,
            scale: 1,
            y: [0, -10, 0],
            rotate: [0, 5, -5, 0]
          }}
          transition={{
            opacity: { delay: index * 0.2 + 1 },
            scale: { delay: index * 0.2 + 1 },
            y: { duration: 3, repeat: Infinity, ease: "easeInOut", delay: index * 0.3 },
            rotate: { duration: 4, repeat: Infinity, ease: "easeInOut", delay: index * 0.2 }
          }}
          whileHover={{ scale: 1.2 }}
        >
          <div className="relative w-full h-full bg-white/10 backdrop-blur-sm rounded-full p-2 border border-white/20">
            <Image
              src={icon.src}
              alt={icon.name}
              fill
              className="object-contain p-1"
            />
          </div>
        </motion.div>
      ))}

      <div className="container mx-auto px-6 py-20 relative z-30">
        {/* Hero Content - Left Side */}
        <motion.div
          className="absolute left-6 md:left-12 top-1/2 transform -translate-y-1/2 max-w-lg"
          initial={{ opacity: 0, x: -100 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
        >
          <motion.div
            className="flex items-center space-x-3 text-blue-400 mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
          >
            <Palette size={20} />
            <span className="text-sm font-medium">Front-end Developer Portfolio</span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.2 }}
          >
            Providing{' '}
            <span className="gradient-text">the best</span><br />
            Project{' '}
            <span className="gradient-text">Experience</span>
          </motion.h1>

          <motion.p
            className="text-lg text-gray-300 mb-8 leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
          >
            I'm a front-end developer with experience in Website, Mobile and Software development.
            Check out my projects and skills.
          </motion.p>

          <motion.button
            className="group flex items-center space-x-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 px-6 py-3 rounded-lg text-white font-semibold transition-all duration-300 hover-glow"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.6 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Send size={18} className="group-hover:translate-x-1 transition-transform duration-300" />
            <span>Contact Me</span>
          </motion.button>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 2 }}
        >
          <motion.div
            className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
            animate={{
              borderColor: ['#9ca3af', '#3b82f6', '#9ca3af']
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <motion.div
              className="w-1 h-3 bg-gray-400 rounded-full mt-2"
              animate={{
                y: [0, 12, 0],
                backgroundColor: ['#9ca3af', '#3b82f6', '#9ca3af']
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
