import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "MiladiCode - Modern Portfolio",
  description: "Front-end Developer Portfolio - Providing the best project experience with modern web technologies",
  keywords: ["portfolio", "frontend", "developer", "react", "nextjs", "web development"],
  authors: [{ name: "MiladiCode" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth dark">
      <body className="antialiased bg-gray-900 dark:bg-gray-900 bg-white text-gray-900 dark:text-gray-100 overflow-x-hidden transition-colors duration-300">
        {children}
      </body>
    </html>
  );
}
