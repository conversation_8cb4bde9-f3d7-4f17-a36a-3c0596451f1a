# Modern Portfolio - MiladiCode

A modern, responsive portfolio website built with Next.js, TypeScript, Tailwind CSS, and Framer Motion. This is a complete modernization of the original HTML/CSS/JavaScript portfolio, featuring enhanced performance, better accessibility, and modern development practices.

## 🚀 Features

### Modern Tech Stack
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Advanced animations and interactions
- **Lucide React** - Beautiful, customizable icons

### Enhanced User Experience
- **Smooth Animations** - Powered by Framer Motion
- **Responsive Design** - Mobile-first approach
- **Dark/Light Mode** - Theme toggle with system preference detection
- **Interactive Elements** - Hover effects, video playback on hover
- **Scroll Animations** - Elements animate as they come into view
- **Smooth Scrolling** - Enhanced navigation experience

### Performance Optimizations
- **Image Optimization** - Next.js Image component
- **Video Optimization** - Lazy loading and efficient playback
- **Code Splitting** - Automatic bundle optimization
- **SEO Optimized** - Meta tags, semantic HTML
- **Accessibility** - ARIA labels, keyboard navigation

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- npm or yarn

### Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Open in browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
modern-portfolio/
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── components/
│       ├── Header.tsx
│       ├── Hero.tsx
│       ├── InfoSection.tsx
│       ├── ProjectsSection.tsx
│       ├── SkillsSection.tsx
│       ├── ContactSection.tsx
│       ├── Footer.tsx
│       ├── ThemeToggle.tsx
│       └── ScrollToTop.tsx
├── public/
│   ├── images/
│   └── videos/
└── package.json
```

## 🎯 Key Improvements from Original

1. **Modern Framework**: Migrated from vanilla HTML/CSS/JS to Next.js with TypeScript
2. **Component Architecture**: Modular, reusable React components
3. **Enhanced Animations**: Replaced AOS with Framer Motion for better performance
4. **Responsive Design**: Mobile-first approach with Tailwind CSS
5. **Performance**: Image optimization, code splitting, and lazy loading
6. **Accessibility**: ARIA labels, semantic HTML, keyboard navigation
7. **SEO**: Meta tags, structured data, and optimized content
8. **Developer Experience**: TypeScript, ESLint, and modern tooling

## 🚀 Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Built with ❤️ by MiladiCode
