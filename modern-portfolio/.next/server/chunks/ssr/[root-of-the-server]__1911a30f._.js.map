{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Youtube, Github, Linkedin } from 'lucide-react';\nimport Image from 'next/image';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  const socialLinks = [\n    { icon: Youtube, href: '#', label: 'YouTube' },\n    { icon: Github, href: '#', label: 'GitHub' },\n    { icon: Linkedin, href: '#', label: 'LinkedIn' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.8 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled \n            ? 'glass-effect shadow-lg shadow-blue-500/10' \n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"container mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Logo */}\n            <motion.div \n              className=\"flex items-center space-x-3\"\n              whileHover={{ scale: 1.05 }}\n              transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\n            >\n              <div className=\"relative w-10 h-10\">\n                <Image\n                  src=\"/images/miladicode.png\"\n                  alt=\"MiladiCode Logo\"\n                  fill\n                  className=\"object-contain\"\n                />\n              </div>\n              <h2 className=\"text-xl font-bold\">\n                <span className=\"gradient-text\">Miladi</span>Code\n              </h2>\n            </motion.div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              {navItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-300 hover:text-white transition-colors duration-300 relative group\"\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 + 0.3 }}\n                  whileHover={{ y: -2 }}\n                >\n                  {item.name}\n                  <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 transition-all duration-300 group-hover:w-full\"></span>\n                </motion.a>\n              ))}\n            </nav>\n\n            {/* Social Icons */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={social.label}\n                  href={social.href}\n                  className=\"p-2 text-gray-400 hover:text-white transition-colors duration-300 hover-glow rounded-lg\"\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: index * 0.1 + 0.5 }}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                  aria-label={social.label}\n                >\n                  <social.icon size={20} />\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              className=\"md:hidden p-2 text-gray-400 hover:text-white transition-colors duration-300\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              whileTap={{ scale: 0.95 }}\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </motion.button>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Sidebar */}\n      <motion.div\n        className={`fixed top-0 right-0 h-full w-80 glass-effect z-40 transform transition-transform duration-300 md:hidden ${\n          isMenuOpen ? 'translate-x-0' : 'translate-x-full'\n        }`}\n        initial={false}\n        animate={{ x: isMenuOpen ? 0 : '100%' }}\n        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n      >\n        <div className=\"p-6 pt-20\">\n          <nav className=\"space-y-6\">\n            {navItems.map((item, index) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                className=\"block text-lg text-gray-300 hover:text-white transition-colors duration-300\"\n                onClick={() => setIsMenuOpen(false)}\n                initial={{ opacity: 0, x: 50 }}\n                animate={{ opacity: isMenuOpen ? 1 : 0, x: isMenuOpen ? 0 : 50 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </nav>\n\n          <div className=\"flex items-center space-x-4 mt-8\">\n            {socialLinks.map((social, index) => (\n              <motion.a\n                key={social.label}\n                href={social.href}\n                className=\"p-3 text-gray-400 hover:text-white transition-colors duration-300 hover-glow rounded-lg\"\n                initial={{ opacity: 0, scale: 0 }}\n                animate={{ \n                  opacity: isMenuOpen ? 1 : 0, \n                  scale: isMenuOpen ? 1 : 0 \n                }}\n                transition={{ delay: index * 0.1 + 0.3 }}\n                aria-label={social.label}\n              >\n                <social.icon size={20} />\n              </motion.a>\n            ))}\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Mobile Overlay */}\n      {isMenuOpen && (\n        <motion.div\n          className=\"fixed inset-0 bg-black/50 z-30 md:hidden\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          onClick={() => setIsMenuOpen(false)}\n        />\n      )}\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAAK,OAAO;QAAS;QAC3C;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;KAChD;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC/B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,8CACA,kBACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;;kDAE1D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAa;;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ,MAAM;wCAAI;wCACvC,YAAY;4CAAE,GAAG,CAAC;wCAAE;;4CAEnB,KAAK,IAAI;0DACV,8OAAC;gDAAK,WAAU;;;;;;;uCATX,KAAK,IAAI;;;;;;;;;;0CAepB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,OAAO,QAAQ,MAAM;wCAAI;wCACvC,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC,OAAO,IAAI;4CAAC,MAAM;;;;;;uCAVd,OAAO,KAAK;;;;;;;;;;0CAgBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,IAAM,cAAc,CAAC;gCAC9B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,cAAW;0CAEV,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;6FAAS,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,wGAAwG,EAClH,aAAa,kBAAkB,oBAC/B;gBACF,SAAS;gBACT,SAAS;oBAAE,GAAG,aAAa,IAAI;gBAAO;gBACtC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;0BAE1D,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS,aAAa,IAAI;wCAAG,GAAG,aAAa,IAAI;oCAAG;oCAC/D,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEhC,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAapB,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,OAAO,IAAI;oCACjB,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,SAAS;wCACP,SAAS,aAAa,IAAI;wCAC1B,OAAO,aAAa,IAAI;oCAC1B;oCACA,YAAY;wCAAE,OAAO,QAAQ,MAAM;oCAAI;oCACvC,cAAY,OAAO,KAAK;8CAExB,cAAA,8OAAC,OAAO,IAAI;wCAAC,MAAM;;;;;;mCAXd,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;YAmB1B,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,SAAS,IAAM,cAAc;;;;;;;;AAKvC;uCAEe", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Send, Palette } from 'lucide-react';\n\nconst Hero = () => {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Video */}\n      <div className=\"absolute inset-0 z-0\">\n        <video\n          autoPlay\n          loop\n          muted\n          playsInline\n          className=\"w-full h-full object-cover opacity-30\"\n        >\n          <source src=\"/videos/galaxy.mp4\" type=\"video/mp4\" />\n        </video>\n        <div className=\"absolute inset-0 bg-gradient-to-b from-gray-900/50 via-gray-900/30 to-gray-900/80\"></div>\n      </div>\n\n      {/* Floating Blackhole Video */}\n      <motion.div\n        className=\"absolute top-20 right-10 w-32 h-32 md:w-48 md:h-48 rounded-full overflow-hidden z-10\"\n        initial={{ opacity: 0, scale: 0, rotate: 0 }}\n        animate={{ opacity: 1, scale: 1, rotate: 360 }}\n        transition={{ \n          duration: 2, \n          delay: 1,\n          rotate: { duration: 20, repeat: Infinity, ease: \"linear\" }\n        }}\n      >\n        <video\n          autoPlay\n          loop\n          muted\n          playsInline\n          className=\"w-full h-full object-cover\"\n        >\n          <source src=\"/videos/blackhole.mp4\" type=\"video/mp4\" />\n        </video>\n      </motion.div>\n\n      <div className=\"container mx-auto px-6 py-20 relative z-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Hero Content */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -100 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 1, delay: 0.5 }}\n          >\n            <motion.div\n              className=\"flex items-center space-x-3 text-blue-400\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.7 }}\n            >\n              <Palette size={24} />\n              <span className=\"text-lg font-medium\">Front-end Developer Portfolio</span>\n            </motion.div>\n\n            <motion.h1\n              className=\"text-4xl md:text-6xl lg:text-7xl font-bold leading-tight\"\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.9 }}\n            >\n              Providing{' '}\n              <span className=\"gradient-text\">the best</span>{' '}\n              Project{' '}\n              <span className=\"gradient-text\">Experience</span>\n            </motion.h1>\n\n            <motion.p\n              className=\"text-xl text-gray-300 max-w-2xl leading-relaxed\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.1 }}\n            >\n              I'm a front-end developer with experience in Website, Mobile and Software development. \n              Check out my projects and skills.\n            </motion.p>\n\n            <motion.button\n              className=\"group flex items-center space-x-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 px-8 py-4 rounded-full text-white font-semibold transition-all duration-300 hover-glow\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.3 }}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Send size={20} className=\"group-hover:translate-x-1 transition-transform duration-300\" />\n              <span>Contact Me</span>\n            </motion.button>\n          </motion.div>\n\n          {/* Hero Video */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, x: 100 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 1, delay: 0.8 }}\n          >\n            <div className=\"relative rounded-2xl overflow-hidden glass-effect p-4\">\n              <video\n                autoPlay\n                loop\n                muted\n                playsInline\n                className=\"w-full h-auto rounded-xl\"\n              >\n                <source src=\"/videos/hero-video.mp4\" type=\"video/mp4\" />\n              </video>\n              \n              {/* Floating Elements */}\n              <motion.div\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full\"\n                animate={{ \n                  y: [0, -10, 0],\n                  scale: [1, 1.1, 1]\n                }}\n                transition={{ \n                  duration: 2, \n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n              />\n              <motion.div\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-purple-500 rounded-full\"\n                animate={{ \n                  y: [0, 10, 0],\n                  scale: [1, 1.2, 1]\n                }}\n                transition={{ \n                  duration: 2.5, \n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                }}\n              />\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2 }}\n        >\n          <motion.div\n            className=\"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\"\n            animate={{ \n              borderColor: ['#9ca3af', '#3b82f6', '#9ca3af']\n            }}\n            transition={{ \n              duration: 2, \n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }}\n          >\n            <motion.div\n              className=\"w-1 h-3 bg-gray-400 rounded-full mt-2\"\n              animate={{ \n                y: [0, 12, 0],\n                backgroundColor: ['#9ca3af', '#3b82f6', '#9ca3af']\n              }}\n              transition={{ \n                duration: 2, \n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,OAAO;IACX,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,QAAQ;wBACR,IAAI;wBACJ,KAAK;wBACL,WAAW;wBACX,WAAU;kCAEV,cAAA,8OAAC;4BAAO,KAAI;4BAAqB,MAAK;;;;;;;;;;;kCAExC,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAG,QAAQ;gBAAE;gBAC3C,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAG,QAAQ;gBAAI;gBAC7C,YAAY;oBACV,UAAU;oBACV,OAAO;oBACP,QAAQ;wBAAE,UAAU;wBAAI,QAAQ;wBAAU,MAAM;oBAAS;gBAC3D;0BAEA,cAAA,8OAAC;oBACC,QAAQ;oBACR,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,WAAU;8BAEV,cAAA,8OAAC;wBAAO,KAAI;wBAAwB,MAAK;;;;;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAI;gCAC/B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;;kDAEtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAGxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,OAAO;wCAAI;;4CACvC;4CACW;0DACV,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAgB;4CAAI;4CAC5C;0DACR,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDACzC;;;;;;kDAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC1B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAI;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;0CAEtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,QAAQ;4CACR,IAAI;4CACJ,KAAK;4CACL,WAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDAAO,KAAI;gDAAyB,MAAK;;;;;;;;;;;sDAI5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,GAAG;oDAAC;oDAAG,CAAC;oDAAI;iDAAE;gDACd,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CACpB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;;;;;;sDAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,GAAG;oDAAC;oDAAG;oDAAI;iDAAE;gDACb,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CACpB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,OAAO;4CACT;;;;;;;;;;;;;;;;;;;;;;;kCAOR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAE;kCAEtC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,aAAa;oCAAC;oCAAW;oCAAW;iCAAU;4BAChD;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,MAAM;4BACR;sCAEA,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;oCACb,iBAAiB;wCAAC;wCAAW;wCAAW;qCAAU;gCACpD;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/InfoSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Send } from 'lucide-react';\nimport Image from 'next/image';\n\nconst InfoSection = () => {\n  const cards = [\n    {\n      title: \"Hi there, I'm Milad\",\n      description: \"With 4 years of experience, I have honed my skills in both frontend and backend dev, creating dynamic and responsive websites.\",\n      image: \"/images/grid1.png\",\n      type: \"image\"\n    },\n    {\n      title: \"Tech Stack\",\n      description: \"I specialize in a variety of languages, frameworks, and tools that allow me to build robust and scalable applications.\",\n      image: \"/images/grid2.png\",\n      type: \"image\"\n    },\n    {\n      title: \"I'm very flexible with time zone communications & locations\",\n      description: \"I'm based in Italy, Bari and open to remote work worldwide.\",\n      image: \"/videos/glob.mp4\",\n      type: \"video\",\n      hasButton: true\n    },\n    {\n      title: \"My Passion for Coding\",\n      description: \"I love solving problems and building things through code. Programming isn't just my profession—it's my passion. I enjoy exploring new technologies, and enhancing my skills.\",\n      image: \"/images/grid4.png\",\n      type: \"image\"\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { \n      opacity: 0, \n      y: 50,\n      scale: 0.9\n    },\n    visible: { \n      opacity: 1, \n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"about\" className=\"section-padding bg-gradient-to-b from-gray-900 to-gray-800\">\n      <div className=\"container mx-auto\">\n        <motion.h1\n          className=\"text-4xl md:text-5xl font-bold text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          Hello, There 👋\n        </motion.h1>\n\n        <motion.div\n          className=\"grid md:grid-cols-2 gap-8\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {cards.map((card, index) => (\n            <motion.div\n              key={index}\n              className=\"glass-effect rounded-2xl p-8 hover-glow group cursor-pointer\"\n              variants={cardVariants}\n              whileHover={{ \n                scale: 1.02,\n                transition: { duration: 0.2 }\n              }}\n            >\n              <div className=\"space-y-6\">\n                <h2 className=\"text-2xl font-bold text-white group-hover:gradient-text transition-all duration-300\">\n                  {card.title}\n                </h2>\n                \n                <p className=\"text-gray-300 leading-relaxed\">\n                  {card.description}\n                </p>\n\n                <div className=\"relative rounded-xl overflow-hidden\">\n                  {card.type === \"video\" ? (\n                    <video\n                      autoPlay\n                      loop\n                      muted\n                      playsInline\n                      className=\"w-full h-48 object-cover\"\n                    >\n                      <source src={card.image} type=\"video/mp4\" />\n                    </video>\n                  ) : (\n                    <div className=\"relative w-full h-48\">\n                      <Image\n                        src={card.image}\n                        alt={card.title}\n                        fill\n                        className=\"object-cover group-hover:scale-105 transition-transform duration-500\"\n                      />\n                    </div>\n                  )}\n                  \n                  {/* Overlay gradient */}\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                </div>\n\n                {card.hasButton && (\n                  <motion.button\n                    className=\"flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 px-6 py-3 rounded-full text-white font-semibold transition-all duration-300\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <Send size={16} />\n                    <span>Contact Me</span>\n                  </motion.button>\n                )}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default InfoSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,cAAc;IAClB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;YACN,WAAW;QACb;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BACxB;;;;;;8BAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;8BAExC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;4BACV,YAAY;gCACV,OAAO;gCACP,YAAY;oCAAE,UAAU;gCAAI;4BAC9B;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAGb,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,KAAK,wBACb,8OAAC;gDACC,QAAQ;gDACR,IAAI;gDACJ,KAAK;gDACL,WAAW;gDACX,WAAU;0DAEV,cAAA,8OAAC;oDAAO,KAAK,KAAK,KAAK;oDAAE,MAAK;;;;;;;;;;yGAGhC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAMhB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;oCAGhB,KAAK,SAAS,kBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;0DACZ,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;2BAlDP;;;;;;;;;;;;;;;;;;;;;AA4DnB;uCAEe", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/ProjectsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ExternalLink, Play, Pause } from 'lucide-react';\n\nconst ProjectsSection = () => {\n  const [hoveredProject, setHoveredProject] = useState<number | null>(null);\n  const [playingVideos, setPlayingVideos] = useState<Set<number>>(new Set());\n\n  const projects = [\n    {\n      title: \"Animated Gaming Website\",\n      description: \"A modern gaming website with stunning animations, interactive elements, and responsive design. Built with React and advanced CSS animations to create an immersive user experience.\",\n      video: \"/videos/project1.mp4\",\n      technologies: [\"React\", \"CSS3\", \"JavaScript\", \"GSAP\"],\n      link: \"#\"\n    },\n    {\n      title: \"Modern Portfolio Website\",\n      description: \"A sleek and professional portfolio website showcasing modern web development techniques. Features smooth scrolling, dynamic content, and optimized performance.\",\n      video: \"/videos/project2.mp4\",\n      technologies: [\"Next.js\", \"TypeScript\", \"Tailwind CSS\", \"Framer Motion\"],\n      link: \"#\"\n    },\n    {\n      title: \"Movie Landing Page\",\n      description: \"An engaging movie landing page with cinematic design elements, trailer integration, and interactive user interface. Optimized for both desktop and mobile viewing.\",\n      video: \"/videos/project3.mp4\",\n      technologies: [\"Vue.js\", \"SCSS\", \"Node.js\", \"MongoDB\"],\n      link: \"#\"\n    }\n  ];\n\n  const handleVideoHover = (index: number, isHovering: boolean) => {\n    setHoveredProject(isHovering ? index : null);\n    \n    const video = document.getElementById(`project-video-${index}`) as HTMLVideoElement;\n    if (video) {\n      if (isHovering) {\n        video.play();\n        setPlayingVideos(prev => new Set(prev).add(index));\n      } else {\n        video.pause();\n        setPlayingVideos(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(index);\n          return newSet;\n        });\n      }\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3\n      }\n    }\n  };\n\n  const projectVariants = {\n    hidden: { \n      opacity: 0, \n      y: 100 \n    },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"projects\" className=\"section-padding bg-gray-900\">\n      <div className=\"container mx-auto\">\n        <motion.h1\n          className=\"text-4xl md:text-5xl font-bold text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          My Projects 👨‍💻\n        </motion.h1>\n\n        <motion.div\n          className=\"space-y-24\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {projects.map((project, index) => (\n            <motion.div\n              key={index}\n              className={`grid lg:grid-cols-2 gap-12 items-center ${\n                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''\n              }`}\n              variants={projectVariants}\n            >\n              {/* Project Video */}\n              <motion.div\n                className={`relative group ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}\n                onMouseEnter={() => handleVideoHover(index, true)}\n                onMouseLeave={() => handleVideoHover(index, false)}\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"relative rounded-2xl overflow-hidden glass-effect p-4\">\n                  <video\n                    id={`project-video-${index}`}\n                    loop\n                    muted\n                    playsInline\n                    className=\"w-full h-auto rounded-xl\"\n                  >\n                    <source src={project.video} type=\"video/mp4\" />\n                  </video>\n                  \n                  {/* Video Overlay */}\n                  <div className={`absolute inset-4 rounded-xl bg-black/30 flex items-center justify-center transition-opacity duration-300 ${\n                    hoveredProject === index ? 'opacity-0' : 'opacity-100'\n                  }`}>\n                    <motion.div\n                      className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      {playingVideos.has(index) ? (\n                        <Pause size={24} className=\"text-white ml-1\" />\n                      ) : (\n                        <Play size={24} className=\"text-white ml-1\" />\n                      )}\n                    </motion.div>\n                  </div>\n\n                  {/* Hover indicator */}\n                  <motion.div\n                    className=\"absolute top-8 right-8 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium\"\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ \n                      opacity: hoveredProject === index ? 1 : 0,\n                      scale: hoveredProject === index ? 1 : 0\n                    }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    Hover to play\n                  </motion.div>\n                </div>\n              </motion.div>\n\n              {/* Project Info */}\n              <motion.div\n                className={`space-y-6 ${index % 2 === 1 ? 'lg:col-start-1' : ''}`}\n                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n                viewport={{ once: true }}\n              >\n                <h2 className=\"text-3xl md:text-4xl font-bold\">\n                  {project.title.split(' ').map((word, wordIndex) => (\n                    <span key={wordIndex}>\n                      {word === 'Gaming' || word === 'Portfolio' || word === 'Landing' ? (\n                        <span className=\"gradient-text\">{word}</span>\n                      ) : (\n                        word\n                      )}{' '}\n                    </span>\n                  ))}\n                </h2>\n\n                <p className=\"text-gray-300 text-lg leading-relaxed\">\n                  {project.description}\n                </p>\n\n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-3\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <motion.span\n                      key={techIndex}\n                      className=\"px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-sm font-medium text-blue-300\"\n                      initial={{ opacity: 0, scale: 0 }}\n                      whileInView={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: techIndex * 0.1 + 0.5 }}\n                      viewport={{ once: true }}\n                      whileHover={{ scale: 1.05 }}\n                    >\n                      {tech}\n                    </motion.span>\n                  ))}\n                </div>\n\n                <motion.button\n                  className=\"group flex items-center space-x-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 px-8 py-4 rounded-full text-white font-semibold transition-all duration-300 hover-glow\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <ExternalLink size={20} className=\"group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300\" />\n                  <span>View Website</span>\n                </motion.button>\n              </motion.div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProjectsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEpE,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAS;gBAAQ;gBAAc;aAAO;YACrD,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAW;gBAAc;gBAAgB;aAAgB;YACxE,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAU;gBAAQ;gBAAW;aAAU;YACtD,MAAM;QACR;KACD;IAED,MAAM,mBAAmB,CAAC,OAAe;QACvC,kBAAkB,aAAa,QAAQ;QAEvC,MAAM,QAAQ,SAAS,cAAc,CAAC,CAAC,cAAc,EAAE,OAAO;QAC9D,IAAI,OAAO;YACT,IAAI,YAAY;gBACd,MAAM,IAAI;gBACV,iBAAiB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;YAC7C,OAAO;gBACL,MAAM,KAAK;gBACX,iBAAiB,CAAA;oBACf,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,MAAM,CAAC;oBACd,OAAO;gBACT;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BACxB;;;;;;8BAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;8BAExC,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,CAAC,wCAAwC,EAClD,QAAQ,MAAM,IAAI,2BAA2B,IAC7C;4BACF,UAAU;;8CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,CAAC,eAAe,EAAE,QAAQ,MAAM,IAAI,mBAAmB,IAAI;oCACtE,cAAc,IAAM,iBAAiB,OAAO;oCAC5C,cAAc,IAAM,iBAAiB,OAAO;oCAC5C,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,IAAI,CAAC,cAAc,EAAE,OAAO;gDAC5B,IAAI;gDACJ,KAAK;gDACL,WAAW;gDACX,WAAU;0DAEV,cAAA,8OAAC;oDAAO,KAAK,QAAQ,KAAK;oDAAE,MAAK;;;;;;;;;;;0DAInC,8OAAC;gDAAI,WAAW,CAAC,yGAAyG,EACxH,mBAAmB,QAAQ,cAAc,eACzC;0DACA,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEtB,cAAc,GAAG,CAAC,uBACjB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;iHAE3B,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;;;;;;;;;;;0DAMhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,SAAS;oDACP,SAAS,mBAAmB,QAAQ,IAAI;oDACxC,OAAO,mBAAmB,QAAQ,IAAI;gDACxC;gDACA,YAAY;oDAAE,UAAU;gDAAI;0DAC7B;;;;;;;;;;;;;;;;;8CAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,CAAC,UAAU,EAAE,QAAQ,MAAM,IAAI,mBAAmB,IAAI;oCACjE,SAAS;wCAAE,SAAS;wCAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;oCAAG;oCACrD,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,0BACnC,8OAAC;;wDACE,SAAS,YAAY,SAAS,eAAe,SAAS,0BACrD,8OAAC;4DAAK,WAAU;sEAAiB;;;;;uGAEjC;wDACC;;mDALM;;;;;;;;;;sDAUf,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDAEV,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,OAAO,YAAY,MAAM;oDAAI;oDAC3C,UAAU;wDAAE,MAAM;oDAAK;oDACvB,YAAY;wDAAE,OAAO;oDAAK;8DAEzB;mDARI;;;;;;;;;;sDAaX,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAClC,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;2BAxGL;;;;;;;;;;;;;;;;;;;;;AAiHnB;uCAEe", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/SkillsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { <PERSON><PERSON><PERSON>, Code } from 'lucide-react';\nimport Image from 'next/image';\n\nconst SkillsSection = () => {\n  const skills = [\n    { name: 'HTML5', image: '/images/1.png' },\n    { name: 'CSS3', image: '/images/2.png' },\n    { name: 'JavaScript', image: '/images/3.webp' },\n    { name: 'React', image: '/images/4.webp' },\n    { name: 'Node.js', image: '/images/5.png' },\n    { name: 'MongoDB', image: '/images/6.png' },\n    { name: 'Git', image: '/images/7.png' },\n    { name: 'Figma', image: '/images/8.png' },\n    { name: 'TypeScript', image: '/images/9.png' },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const skillVariants = {\n    hidden: { \n      opacity: 0, \n      scale: 0,\n      rotate: -180\n    },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      rotate: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"skills\" className=\"section-padding bg-gradient-to-b from-gray-800 to-gray-900\">\n      <div className=\"container mx-auto\">\n        <motion.h1\n          className=\"text-4xl md:text-5xl font-bold text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          My Skills 💪\n        </motion.h1>\n\n        <div className=\"grid lg:grid-cols-3 gap-12 items-center\">\n          {/* Designer Section */}\n          <motion.div\n            className=\"space-y-6\"\n            initial={{ opacity: 0, x: -100 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center space-x-3\">\n              <h2 className=\"text-3xl font-bold gradient-text\">Designer</h2>\n              <Laptop size={32} className=\"text-blue-400\" />\n            </div>\n            <p className=\"text-gray-300 leading-relaxed\">\n              I have expertise in HTML, CSS, JavaScript, and design tools like Figma and Adobe XD. \n              My strength lies in blending aesthetics with functionality to create seamless user experiences.\n            </p>\n          </motion.div>\n\n          {/* Skills Carousel */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1 }}\n            viewport={{ once: true }}\n          >\n            {/* Central Brain Image */}\n            <div className=\"relative w-64 h-64 mx-auto mb-8\">\n              <Image\n                src=\"/images/digital brain (2).png\"\n                alt=\"Digital Brain\"\n                fill\n                className=\"object-contain animate-float\"\n              />\n            </div>\n\n            {/* Orbiting Skills */}\n            <div className=\"relative w-80 h-80 mx-auto\">\n              <motion.div\n                className=\"absolute inset-0\"\n                variants={containerVariants}\n                initial=\"hidden\"\n                whileInView=\"visible\"\n                viewport={{ once: true }}\n                animate={{ rotate: 360 }}\n                transition={{ \n                  rotate: { duration: 20, repeat: Infinity, ease: \"linear\" }\n                }}\n              >\n                {skills.map((skill, index) => {\n                  const angle = (index * 360) / skills.length;\n                  const radius = 140;\n                  const x = Math.cos((angle * Math.PI) / 180) * radius;\n                  const y = Math.sin((angle * Math.PI) / 180) * radius;\n\n                  return (\n                    <motion.div\n                      key={skill.name}\n                      className=\"absolute w-16 h-16 glass-effect rounded-full p-2 hover-glow group cursor-pointer\"\n                      style={{\n                        left: `calc(50% + ${x}px - 32px)`,\n                        top: `calc(50% + ${y}px - 32px)`,\n                      }}\n                      variants={skillVariants}\n                      whileHover={{ \n                        scale: 1.2,\n                        zIndex: 10\n                      }}\n                      animate={{ \n                        rotate: -360,\n                      }}\n                      transition={{ \n                        rotate: { duration: 20, repeat: Infinity, ease: \"linear\" }\n                      }}\n                    >\n                      <div className=\"relative w-full h-full\">\n                        <Image\n                          src={skill.image}\n                          alt={skill.name}\n                          fill\n                          className=\"object-contain rounded-full\"\n                        />\n                      </div>\n                      \n                      {/* Tooltip */}\n                      <div className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-20\">\n                        {skill.name}\n                      </div>\n                    </motion.div>\n                  );\n                })}\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Coder Section */}\n          <motion.div\n            className=\"space-y-6\"\n            initial={{ opacity: 0, x: 100 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center space-x-3\">\n              <h2 className=\"text-3xl font-bold gradient-text\">Coder</h2>\n              <Code size={32} className=\"text-purple-400\" />\n            </div>\n            <p className=\"text-gray-300 leading-relaxed\">\n              I'm skilled in HTML, CSS, JavaScript, and frameworks like React and Node.js. \n              I also have experience with database management using MongoDB and MySQL.\n            </p>\n          </motion.div>\n        </div>\n\n        {/* Additional Skills Grid */}\n        <motion.div\n          className=\"mt-20 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {skills.slice(0, 6).map((skill, index) => (\n            <motion.div\n              key={`grid-${skill.name}`}\n              className=\"glass-effect rounded-xl p-4 hover-glow group cursor-pointer\"\n              variants={skillVariants}\n              whileHover={{ \n                scale: 1.05,\n                rotate: [0, -5, 5, 0]\n              }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"relative w-12 h-12 mx-auto mb-3\">\n                <Image\n                  src={skill.image}\n                  alt={skill.name}\n                  fill\n                  className=\"object-contain group-hover:scale-110 transition-transform duration-300\"\n                />\n              </div>\n              <p className=\"text-center text-sm font-medium text-gray-300 group-hover:text-white transition-colors duration-300\">\n                {skill.name}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default SkillsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,gBAAgB;IACpB,MAAM,SAAS;QACb;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAQ,OAAO;QAAgB;QACvC;YAAE,MAAM;YAAc,OAAO;QAAiB;QAC9C;YAAE,MAAM;YAAS,OAAO;QAAiB;QACzC;YAAE,MAAM;YAAW,OAAO;QAAgB;QAC1C;YAAE,MAAM;YAAW,OAAO;QAAgB;QAC1C;YAAE,MAAM;YAAO,OAAO;QAAgB;QACtC;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAc,OAAO;QAAgB;KAC9C;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,QAAQ,CAAC;QACX;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BACxB;;;;;;8BAID,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAI;4BAC/B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;8CAE9B,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAO/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;4BAAK;;8CAGvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;wCACV,SAAQ;wCACR,aAAY;wCACZ,UAAU;4CAAE,MAAM;wCAAK;wCACvB,SAAS;4CAAE,QAAQ;wCAAI;wCACvB,YAAY;4CACV,QAAQ;gDAAE,UAAU;gDAAI,QAAQ;gDAAU,MAAM;4CAAS;wCAC3D;kDAEC,OAAO,GAAG,CAAC,CAAC,OAAO;4CAClB,MAAM,QAAQ,AAAC,QAAQ,MAAO,OAAO,MAAM;4CAC3C,MAAM,SAAS;4CACf,MAAM,IAAI,KAAK,GAAG,CAAC,AAAC,QAAQ,KAAK,EAAE,GAAI,OAAO;4CAC9C,MAAM,IAAI,KAAK,GAAG,CAAC,AAAC,QAAQ,KAAK,EAAE,GAAI,OAAO;4CAE9C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,OAAO;oDACL,MAAM,CAAC,WAAW,EAAE,EAAE,UAAU,CAAC;oDACjC,KAAK,CAAC,WAAW,EAAE,EAAE,UAAU,CAAC;gDAClC;gDACA,UAAU;gDACV,YAAY;oDACV,OAAO;oDACP,QAAQ;gDACV;gDACA,SAAS;oDACP,QAAQ,CAAC;gDACX;gDACA,YAAY;oDACV,QAAQ;wDAAE,UAAU;wDAAI,QAAQ;wDAAU,MAAM;oDAAS;gDAC3D;;kEAEA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,MAAM,KAAK;4DAChB,KAAK,MAAM,IAAI;4DACf,IAAI;4DACJ,WAAU;;;;;;;;;;;kEAKd,8OAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI;;;;;;;+CA7BR,MAAM,IAAI;;;;;wCAiCrB;;;;;;;;;;;;;;;;;sCAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAI;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;8CAE5B,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAQjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;8BAExC,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;4BACV,YAAY;gCACV,OAAO;gCACP,QAAQ;oCAAC;oCAAG,CAAC;oCAAG;oCAAG;iCAAE;4BACvB;4BACA,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,MAAM,KAAK;wCAChB,KAAK,MAAM,IAAI;wCACf,IAAI;wCACJ,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAE,WAAU;8CACV,MAAM,IAAI;;;;;;;2BAlBR,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;AA0BvC;uCAEe", "debugId": null}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Phone, Send, Linkedin, Youtube, Twitter, Facebook } from 'lucide-react';\n\nconst ContactSection = () => {\n  const [formData, setFormData] = useState({\n    fullName: '',\n    email: '',\n    message: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    console.log('Form submitted:', formData);\n    setIsSubmitting(false);\n    \n    // Reset form\n    setFormData({\n      fullName: '',\n      email: '',\n      message: ''\n    });\n  };\n\n  const contactInfo = [\n    {\n      icon: Phone,\n      text: '+39 111 222 444',\n      href: 'tel:+39111222444'\n    },\n    {\n      icon: Send,\n      text: 'Contact Example.com',\n      href: 'mailto:<EMAIL>'\n    },\n    {\n      icon: Linkedin,\n      text: 'LinkedIn Profile',\n      href: '#'\n    }\n  ];\n\n  const socialLinks = [\n    { icon: Youtube, href: '#', label: 'YouTube' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: Facebook, href: '#', label: 'Facebook' },\n  ];\n\n  return (\n    <section id=\"contact\" className=\"section-padding bg-gradient-to-b from-gray-900 to-black\">\n      <div className=\"container mx-auto\">\n        <motion.h1\n          className=\"text-4xl md:text-5xl font-bold text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          Let's talk 😊\n        </motion.h1>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Info */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -100 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"glass-effect rounded-2xl p-8 space-y-6\">\n              {contactInfo.map((info, index) => (\n                <motion.a\n                  key={index}\n                  href={info.href}\n                  className=\"flex items-center space-x-4 text-gray-300 hover:text-white transition-colors duration-300 group\"\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 + 0.3 }}\n                  viewport={{ once: true }}\n                  whileHover={{ x: 10 }}\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                    <info.icon size={20} className=\"text-white\" />\n                  </div>\n                  <span className=\"text-lg\">{info.text}</span>\n                </motion.a>\n              ))}\n\n              <div className=\"flex items-center space-x-4 pt-4\">\n                {socialLinks.map((social, index) => (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    className=\"w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-all duration-300\"\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ delay: index * 0.1 + 0.6 }}\n                    viewport={{ once: true }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                    aria-label={social.label}\n                  >\n                    <social.icon size={20} />\n                  </motion.a>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            className=\"glass-effect rounded-2xl p-8\"\n            initial={{ opacity: 0, x: 100 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <p className=\"text-gray-300 mb-8 leading-relaxed\">\n              Whether you're looking to build a new website, improve your existing platform, \n              or bring a unique project to life, I'm here to help.\n            </p>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n                viewport={{ once: true }}\n              >\n                <label className=\"block text-gray-300 mb-2 font-medium\">\n                  Full Name\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"fullName\"\n                  value={formData.fullName}\n                  onChange={handleInputChange}\n                  placeholder=\"Your Full Name\"\n                  className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300\"\n                  required\n                />\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n                viewport={{ once: true }}\n              >\n                <label className=\"block text-gray-300 mb-2 font-medium\">\n                  Email Address\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  placeholder=\"Your Email\"\n                  className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300\"\n                  required\n                />\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n                viewport={{ once: true }}\n              >\n                <label className=\"block text-gray-300 mb-2 font-medium\">\n                  Your Message\n                </label>\n                <textarea\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleInputChange}\n                  placeholder=\"Share your thoughts...\"\n                  rows={5}\n                  className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 resize-none\"\n                  required\n                />\n              </motion.div>\n\n              <motion.button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"w-full flex items-center justify-center space-x-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 px-8 py-4 rounded-lg text-white font-semibold transition-all duration-300 hover-glow disabled:cursor-not-allowed\"\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.5 }}\n                viewport={{ once: true }}\n                whileHover={{ scale: isSubmitting ? 1 : 1.02 }}\n                whileTap={{ scale: isSubmitting ? 1 : 0.98 }}\n              >\n                {isSubmitting ? (\n                  <>\n                    <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                    <span>Sending...</span>\n                  </>\n                ) : (\n                  <>\n                    <span>Send Message</span>\n                    <Send size={20} />\n                  </>\n                )}\n              </motion.button>\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContactSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,iBAAiB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,OAAO;QACP,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,gBAAgB;QAEhB,aAAa;QACb,YAAY;YACV,UAAU;YACV,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,MAAM;YACN,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;KAChD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BACxB;;;;;;8BAID,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAI;4BAC/B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO,QAAQ,MAAM;4CAAI;4CACvC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,GAAG;4CAAG;;8DAEpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,KAAK,IAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;;;;;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAW,KAAK,IAAI;;;;;;;2CAZ/B;;;;;kDAgBT,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,OAAO,IAAI;gDACjB,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,YAAY;oDAAE,OAAO,QAAQ,MAAM;gDAAI;gDACvC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAK;gDACxB,cAAY,OAAO,KAAK;0DAExB,cAAA,8OAAC,OAAO,IAAI;oDAAC,MAAM;;;;;;+CAXd,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;sCAmB3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAI;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAKlD,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,MAAM;4CAAK;;8DAEvB,8OAAC;oDAAM,WAAU;8DAAuC;;;;;;8DAGxD,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,MAAM;4CAAK;;8DAEvB,8OAAC;oDAAM,WAAU;8DAAuC;;;;;;8DAGxD,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,MAAM;4CAAK;;8DAEvB,8OAAC;oDAAM,WAAU;8DAAuC;;;;;;8DAGxD,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,aAAY;oDACZ,MAAM;oDACN,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,UAAU;4CACV,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,OAAO,eAAe,IAAI;4CAAK;4CAC7C,UAAU;gDAAE,OAAO,eAAe,IAAI;4CAAK;sDAE1C,6BACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;6EAGR;;kEACE,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhC;uCAEe", "debugId": null}}, {"offset": {"line": 2375, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/man/Animated-Portfolio/modern-portfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Heart, Youtube, Github, Linkedin } from 'lucide-react';\n\nconst Footer = () => {\n  const socialLinks = [\n    { icon: Youtube, href: '#', label: 'YouTube' },\n    { icon: Github, href: '#', label: 'GitHub' },\n    { icon: Linkedin, href: '#', label: 'LinkedIn' },\n  ];\n\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-black py-12\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          className=\"flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0\"\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          {/* Copyright */}\n          <motion.div\n            className=\"flex items-center space-x-2 text-gray-400\"\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <span>©️ {currentYear}, Made with</span>\n            <motion.div\n              animate={{ \n                scale: [1, 1.2, 1],\n                color: ['#ef4444', '#ec4899', '#ef4444']\n              }}\n              transition={{ \n                duration: 2, \n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            >\n              <Heart size={16} className=\"text-red-500 fill-current\" />\n            </motion.div>\n            <span>by</span>\n            <span className=\"gradient-text font-semibold\">MiladiCode</span>\n          </motion.div>\n\n          {/* Social Links */}\n          <motion.div\n            className=\"flex items-center space-x-4\"\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            {socialLinks.map((social, index) => (\n              <motion.a\n                key={social.label}\n                href={social.href}\n                className=\"w-10 h-10 bg-gray-800 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-all duration-300 hover-glow\"\n                initial={{ opacity: 0, scale: 0 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ delay: index * 0.1 + 0.6 }}\n                viewport={{ once: true }}\n                whileHover={{ \n                  scale: 1.1,\n                  rotate: [0, -10, 10, 0]\n                }}\n                whileTap={{ scale: 0.95 }}\n                aria-label={social.label}\n              >\n                <social.icon size={18} />\n              </motion.a>\n            ))}\n          </motion.div>\n        </motion.div>\n\n        {/* Divider */}\n        <motion.div\n          className=\"mt-8 pt-8 border-t border-gray-800\"\n          initial={{ opacity: 0, scaleX: 0 }}\n          whileInView={{ opacity: 1, scaleX: 1 }}\n          transition={{ duration: 1, delay: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"text-center text-gray-500 text-sm\">\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1 }}\n              viewport={{ once: true }}\n            >\n              Built with Next.js, TypeScript, Tailwind CSS, and Framer Motion\n            </motion.p>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAAK,OAAO;QAAS;QAC3C;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;KAChD;IAED,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAGvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;;wCAAK;wCAAI;wCAAY;;;;;;;8CACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,OAAO;4CAAC;4CAAW;4CAAW;yCAAU;oCAC1C;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;8CAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAE7B,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;sCAIhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEtB,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,OAAO,IAAI;oCACjB,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,OAAO,QAAQ,MAAM;oCAAI;oCACvC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCACV,OAAO;wCACP,QAAQ;4CAAC;4CAAG,CAAC;4CAAI;4CAAI;yCAAE;oCACzB;oCACA,UAAU;wCAAE,OAAO;oCAAK;oCACxB,cAAY,OAAO,KAAK;8CAExB,cAAA,8OAAC,OAAO,IAAI;wCAAC,MAAM;;;;;;mCAdd,OAAO,KAAK;;;;;;;;;;;;;;;;8BAqBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,aAAa;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACrC,YAAY;wBAAE,UAAU;wBAAG,OAAO;oBAAI;oBACtC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAE;4BACtC,UAAU;gCAAE,MAAM;4BAAK;sCACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}]}